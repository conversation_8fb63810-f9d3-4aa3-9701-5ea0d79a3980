/* models */
const InventoryHistory = require('../models/inventory-history.model');
const Equipment = require('../models/equipment.model');
const constantUtils = require('../utils/constants.utils');

/* Command to run script
-> npm run seeder update-inventory-tracker
*/

/**
 * Update inventory history tracker field to "Nieuw-Vennep" 
 * for all records where tracker is not equal to "Nieuw-Vennep"
 *
 * @returns
 */
exports.up = async () => {
  try {
    console.log(constantUtils.START_PROCESSING);

    // Find all inventory history records where tracker is not "Nieuw-Vennep"
    const filter = {
      deletedAt: null,
      tracker: { $ne: "Nieuw-Vennep" }
    };

    // First, get count of records that need to be updated
    const totalRecords = await InventoryHistory.countDocuments(filter);
    console.log(`Found ${totalRecords} inventory history records that need tracker update`);

    if (totalRecords === 0) {
      console.log('No records need to be updated. All trackers are already set to "Nieuw-Vennep"');
      return true;
    }

    // Get some sample records to show what will be updated
    const sampleRecords = await InventoryHistory.find(filter)
      .limit(10)
      .select('_id tracker status type equipment')
      .populate('equipment', 'name');

    console.log('\nSample records to be updated:');
    sampleRecords.forEach((record, index) => {
      console.log(`${index + 1}. ID: ${record._id}, Current Tracker: "${record.tracker || 'null'}", Equipment: ${record.equipment?.name || 'N/A'}, Status: ${record.status}, Type: ${record.type}`);
    });

    // Update all records where tracker is not "Nieuw-Vennep"
    const updateResult = await InventoryHistory.updateMany(
      filter,
      {
        $set: {
          tracker: "Nieuw-Vennep",
          updatedAt: new Date()
        }
      }
    );

    console.log(constantUtils.END_PROCESS);
    console.log(`Successfully updated tracker to "Nieuw-Vennep" for ${updateResult.modifiedCount} inventory history records`);
    
    if (updateResult.modifiedCount !== totalRecords) {
      console.log(`Warning: Expected to update ${totalRecords} records, but actually updated ${updateResult.modifiedCount} records`);
    }

    // Verify the update by checking if any records still have tracker != "Nieuw-Vennep"
    const remainingRecords = await InventoryHistory.countDocuments({
      deletedAt: null,
      tracker: { $ne: "Nieuw-Vennep" }
    });

    if (remainingRecords === 0) {
      console.log('✅ Verification successful: All inventory history records now have tracker set to "Nieuw-Vennep"');
    } else {
      console.log(`⚠️  Warning: ${remainingRecords} records still have tracker != "Nieuw-Vennep"`);
    }

    return true;
  } catch (error) {
    console.error('Error in update-inventory-tracker seeder:', error);
    throw new Error(error.message);
  }
};

/**
 * Rollback the migration - this seeder doesn't have a meaningful rollback
 * since we don't know what the original tracker values were
 *
 * @returns
 */
exports.down = async () => {
  try {
    console.log('Rollback for update-inventory-tracker seeder...');
    console.log('⚠️  Warning: This seeder cannot be rolled back meaningfully');
    console.log('The original tracker values were not stored and cannot be restored');
    console.log('If you need to restore original values, you would need to restore from a database backup');
    
    return true;
  } catch (error) {
    console.error('Error in rollback:', error);
    throw new Error(error.message);
  }
};
