const mongoose = require('mongoose');

const EquipmentQuantityType = new mongoose.Schema(
  {
    name: {
      type: String,
    },
    priceType: {
      type: String,
      enum: ['buy', 'rental'],
    },
    quantityType: {
      type: String,
      enum: ['unique', 'multiple'],
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isTemporary: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('equipment-quantity-type', EquipmentQuantityType);
