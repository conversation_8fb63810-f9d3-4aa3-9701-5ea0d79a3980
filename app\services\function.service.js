const functionModel = require('../models/function.model');

/**
 * Create Function
 *
 * @param {*} function
 * @returns
 */
exports.createFunction = async functionData => {
  return await functionModel.create(functionData);
};

/**
 * Update By Id
 *
 * @param {*} id
 * @param {*} function
 * @returns
 */
exports.updateFunction = async (id, functionData) => {
  return functionModel.findByIdAndUpdate(id, { $set: functionData }, { new: true }).populate([
    {
      path: 'project project',
      select: { title: 1, _id: 1 },
      strictPopulate: false,
    },
    {
      path: 'account account',
      select: { name: 1, _id: 1 },
      strictPopulate: false,
    },
    {
      path: 'certificates',
      select: { name: 1, _id: 1 },
      model: 'certificate-type',
    },
  ]);
};

/**
 * Delete By Id
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteFunction = async (id, deletedAt) => {
  return functionModel.findByIdAndUpdate({ _id: id }, { $set: deletedAt });
};

/**
 * Get All Function
 *
 * @returns
 */
exports.getAllFunction = async (
  filter = {},
  page = null,
  perPage = null,
  sort = { sortOrder: 1 }
) => {
  let query = functionModel
    .find(filter, { _id: 0, createdAt: 0, updatedAt: 0, __v: 0 })
    .collation({ locale: 'en', strength: 2 })
    .sort(sort)
    .limit(perPage)
    .skip(page * perPage)
    .select('_id')
    .sort({ sortOrder: 1 })
    .populate([
      {
        path: 'project project',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'account account',
        select: { name: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'certificates',
        select: { name: 1, _id: 1 },
        model: 'certificate-type',
      },
    ]);

  if (perPage && page) {
    query = query.limit(perPage).skip(page * perPage);
  }

  return query;
};

/**
 * Get Function By Id
 *
 * @param {*} id
 * @returns
 */
exports.getFunctionById = async (id, account) => {
  return functionModel
    .findOne(
      {
        $and: [{ _id: id }, { account: account }],
      },
      { _id: 0, createdAt: 0, updatedAt: 0, __v: 0 }
    )
    .populate([
      {
        path: 'project project',
        select: { title: 1, _id: 1 },
        strictPopulate: false,
      },
      {
        path: 'account account',
        select: { name: 1, _id: 1 },
        strictPopulate: false,
      },
    ]);
};

/**
 * Get Function By Name
 *
 * @param {*} functionName
 * @returns
 */
exports.getFunctionByName = async filter => functionModel.findOne(filter);

/**
 * Get Single function
 *
 * @param {*} functionName
 * @returns
 */
exports.getFunction = async id => {
  return functionModel.findById(id);
};

/**
 * Delete All Project Function
 *
 * @param {*} id
 * @param {*} deletedAt
 * @returns
 */
exports.deleteAllProjectFunction = async (projectId, deletedAt) => {
  return functionModel.updateMany(
    { project: projectId },
    {
      $set: deletedAt,
    }
  );
};

exports.getDefaultFunction = async () => {
  return functionModel.findOne({ isDefault: true });
};

/**
 * Filter Single Function
 *
 * @param {*} filter
 * @returns
 */
exports.filterSingleFunction = async filter => {
  return functionModel.findOne(filter);
};

exports.getFunctionByCertificateId = async certificateId => {
  try {
    const assignedFunction = await functionModel.findOne({
      'certificates.certificate': certificateId,
    });
    return assignedFunction;
  } catch (error) {
    throw new Error(error.message);
  }
};
