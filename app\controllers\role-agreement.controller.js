// services
const roleAgreementService = require('../services/role-agreement.service');

// Utils
const constantUtils = require('../utils/constants.utils');
const responseUtils = require('../utils/response.utils');
const commonUtils = require('../utils/common.utils');

/**
 * Assing Role Agreement
 *
 * @param {*} req
 * @param {*} res
 */
exports.assignRoleAgreement = async (req, res) => {
  try {
    let { agreements } = req.body;

    for (let key in agreements) {
      let { agreement_id, agreement } = agreements[key];

      let exist = await roleAgreementService.getRoleAgreementById(agreement_id);

      if (!exist) {
        return res
          .status(404)
          .json(responseUtils.errorResponse(constantUtils.ROLE_AGREEMENT_NOT_FOUND));
      }

      if (agreement.read === false && (agreement.update === true || agreement.delete === true)) {
        return res
          .status(403)
          .json(
            responseUtils.errorResponse(
              constantUtils.ROLE_AGREEMENT_READ_VALIDATION,
              agreements[key]
            )
          );
      }

      await roleAgreementService.updateRoleAgreement(agreement_id, {
        agreement,
      });
    }

    res.status(200).json(responseUtils.successResponse(constantUtils.ASSIGN_ROLE_AGREEMENT));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};

/**
 * Get Role Agreements By Role
 *
 * @param {*} req
 * @param {*} res
 */
exports.getRoleAgreementByRole = async (req, res) => {
  try {
    let role = req.params.role;

    if (!commonUtils.isValidId(role)) {
      return res.status(400).json(responseUtils.errorResponse(constantUtils.INVALID_ROLE_ID));
    }

    let filterData = {
      role: commonUtils.toObjectId(role),
      isActive: true,
      deletedBy: null,
    };

    const roleAgreements = await roleAgreementService.filterRoleAgreements(filterData);

    res
      .status(200)
      .json(responseUtils.successResponse(constantUtils.ROLE_AGREEMENT_LIST, roleAgreements));
  } catch (err) {
    res.status(500).json(responseUtils.errorResponse(err.message));
  }
};
