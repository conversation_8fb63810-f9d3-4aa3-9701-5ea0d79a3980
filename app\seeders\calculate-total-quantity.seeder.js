/* models */
const Equipment = require('../models/equipment.model');
const EquipmentOrderHistory = require('../models/equipment-order-history.model');
const InventoryHistory = require('../models/inventory-history.model');
const PmManageOrderEquipment = require('../models/pm-order-manage-equipment.model');
const PmOrder = require('../models/pm-order.model');
const EquipmentOrder = require('../models/equipment-order.model');
const ReturnOrderHistory = require('../models/return-order-history.model');
const ReturnOrder = require('../models/return-order.model');
const ShoppingCart = require('../models/shopping-cart.model');
const constantUtils = require('../utils/constants.utils');
const EquipmentType = require('../models/equipment-type.model');
const EquipmentQuantityType = require('../models/equipment-quantity-type.model');

/**
 * Update equipment quantity field by adding wmDispatchQuantity from equipment-order-history
 * New quantity = current equipment.quantity + sum of wmDispatchQuantity
 *
 * @returns
 */
exports.up = async () => {
  try {
    console.log(constantUtils.START_PROCESSING);

    // Find all equipment records
    const equipmentRecords = await Equipment.find({ deletedAt: null });

    console.log(`Found ${equipmentRecords.length} equipment records to process`);

    let updatedCount = 0;
    let errorCount = 0;

    for (const equipment of equipmentRecords) {
      try {
        const originalQuantity = equipment.quantity || 0;

        // Get sum of wmDispatchQuantity from equipment-order-history
        const aggregationResult = await EquipmentOrderHistory.aggregate([
          {
            $match: {
              equipment: equipment._id,
              deletedAt: null,
              wmDispatchQuantity: { $ne: null, $exists: true },
            },
          },
          {
            $group: {
              _id: null,
              totalWmDispatchQuantity: { $sum: '$wmDispatchQuantity' },
            },
          },
        ]);

        const totalWmDispatchQuantity =
          aggregationResult.length > 0 ? aggregationResult[0].totalWmDispatchQuantity || 0 : 0;

        // Calculate new quantity: original quantity + wmDispatchQuantity
        const newQuantity = originalQuantity + totalWmDispatchQuantity;

        // Ensure quantity is not negative
        const finalQuantity = Math.max(0, newQuantity);

        // Update the equipment record with the new quantity
        await Equipment.findByIdAndUpdate(
          equipment._id,
          { quantity: finalQuantity },
          { new: true }
        );

        updatedCount++;

        // Log progress every 100 records
        if (updatedCount % 100 === 0) {
          console.log(`Processed ${updatedCount} equipment records...`);
        }

        console.log(
          `Equipment ID: ${equipment._id}, Name: ${equipment.name}, Original Quantity: ${originalQuantity}, WM Dispatch: ${totalWmDispatchQuantity}, New Quantity: ${finalQuantity}`
        );
      } catch (error) {
        console.error(`Error processing equipment ${equipment._id}:`, error.message);
        errorCount++;
      }
    }

    console.log(constantUtils.END_PROCESS);
    console.log(`Successfully updated quantity for ${updatedCount} equipment records`);

    if (errorCount > 0) {
      console.log(`Encountered errors with ${errorCount} equipment records`);
    }

    // eslint-disable-next-line no-undef
    await Promise.all([
      EquipmentOrder.deleteMany({}),
      PmOrder.deleteMany({}),
      PmManageOrderEquipment.deleteMany({}),
      ShoppingCart.deleteMany({}),
      EquipmentOrderHistory.deleteMany({}),
      ReturnOrder.deleteMany({}),
      ReturnOrderHistory.deleteMany({}),
      InventoryHistory.deleteMany({ type: { $ne: 'purchase' } }),
    ]);

    return true;
  } catch (error) {
    console.error('Error in calculate-total-quantity seeder:', error);
    throw new Error(error.message);
  }
};
