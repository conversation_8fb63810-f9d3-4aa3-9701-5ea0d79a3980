/* models */
const Equipment = require('../models/equipment.model');
const EquipmentOrderHistory = require('../models/equipment-order-history.model');
const InventoryHistory = require('../models/inventory-history.model');
const PmManageOrderEquipment = require('../models/pm-order-manage-equipment.model');
const PmOrder = require('../models/pm-order.model');
const EquipmentOrder = require('../models/equipment-order.model');
const ReturnOrderHistory = require('../models/return-order-history.model');
const ReturnOrder = require('../models/return-order.model');
const ShoppingCart = require('../models/shopping-cart.model');
const constantUtils = require('../utils/constants.utils');
const EquipmentType = require('../models/equipment-type.model');
const EquipmentQuantityType = require('../models/equipment-quantity-type.model');

/**
 * Calculate and populate totalQuantity field for all equipment records
 *
 * @returns
 */
exports.up = async () => {
  try {
    console.log(constantUtils.START_PROCESSING);

    // Find all equipment records with populated equipmentType and quantityType
    const equipmentRecords = await Equipment.find({ deletedAt: null }).populate({
      path: 'equipmentType',
      populate: {
        path: 'quantityType',
        model: 'equipment-quantity-type',
      },
    });

    console.log(`Found ${equipmentRecords.length} equipment records to process`);

    let updatedCount = 0;
    let errorCount = 0;

    for (const equipment of equipmentRecords) {
      try {
        let totalQuantity = 0;
        const equipmentQuantity = equipment.quantity || 0;

        // Check if equipmentType and quantityType exist
        if (!equipment.equipmentType || !equipment.equipmentType.quantityType) {
          console.log(
            `Equipment ID: ${equipment._id} - Missing equipmentType or quantityType, using default calculation`
          );

          // Default calculation: equipment.quantity + sum of wmDispatchQuantity
          const aggregationResult = await EquipmentOrderHistory.aggregate([
            {
              $match: {
                equipment: equipment._id,
                deletedAt: null,
                wmDispatchQuantity: { $ne: null, $exists: true },
              },
            },
            {
              $group: {
                _id: null,
                totalWmDispatchQuantity: { $sum: '$wmDispatchQuantity' },
              },
            },
          ]);

          const totalWmDispatchQuantity =
            aggregationResult.length > 0 ? aggregationResult[0].totalWmDispatchQuantity || 0 : 0;

          totalQuantity = equipmentQuantity + totalWmDispatchQuantity;
        } else {
          const quantityType = equipment.equipmentType.quantityType;
          const priceType = quantityType.priceType; // 'buy' or 'rental'
          const quantityTypeEnum = quantityType.quantityType; // 'unique' or 'multiple'

          console.log(
            `Equipment ID: ${equipment._id} - Price Type: ${priceType}, Quantity Type: ${quantityTypeEnum}`
          );

          if (priceType === 'buy' && quantityTypeEnum === 'unique') {
            // Buy Unique - set quantity to 1
            totalQuantity = 1;
          } else if (priceType === 'rental' && quantityTypeEnum === 'unique') {
            // Rental Unique - set quantity to 1
            totalQuantity = 1;
          } else if (priceType === 'rental' && quantityTypeEnum === 'multiple') {
            // Multiple Rental - sum of equipment quantities
            totalQuantity = equipmentQuantity;
          } else if (priceType === 'buy' && quantityTypeEnum === 'multiple') {
            // Multiple Buy - if checked in, remove quantity from total

            // Get sum of wmDispatchQuantity from equipment-order-history
            const aggregationResult = await EquipmentOrderHistory.aggregate([
              {
                $match: {
                  equipment: equipment._id,
                  deletedAt: null,
                  wmDispatchQuantity: { $ne: null, $exists: true },
                },
              },
              {
                $group: {
                  _id: null,
                  totalWmDispatchQuantity: { $sum: '$wmDispatchQuantity' },
                },
              },
            ]);

            const totalWmDispatchQuantity =
              aggregationResult.length > 0 ? aggregationResult[0].totalWmDispatchQuantity || 0 : 0;

            // Check for checked-in equipment (status: 'check-in')
            const checkedInResult = await EquipmentOrderHistory.aggregate([
              {
                $match: {
                  equipment: equipment._id,
                  deletedAt: null,
                  status: 'check-in',
                  pmReceivedQuantity: { $ne: null, $exists: true },
                },
              },
              {
                $group: {
                  _id: null,
                  totalCheckedInQuantity: { $sum: '$pmReceivedQuantity' },
                },
              },
            ]);

            const totalCheckedInQuantity =
              checkedInResult.length > 0 ? checkedInResult[0].totalCheckedInQuantity || 0 : 0;

            // For Multiple Buy: totalQuantity = equipment.quantity + wmDispatchQuantity - checkedInQuantity
            totalQuantity = equipmentQuantity + totalWmDispatchQuantity - totalCheckedInQuantity;
          } else {
            // Default fallback
            const aggregationResult = await EquipmentOrderHistory.aggregate([
              {
                $match: {
                  equipment: equipment._id,
                  deletedAt: null,
                  wmDispatchQuantity: { $ne: null, $exists: true },
                },
              },
              {
                $group: {
                  _id: null,
                  totalWmDispatchQuantity: { $sum: '$wmDispatchQuantity' },
                },
              },
            ]);

            const totalWmDispatchQuantity =
              aggregationResult.length > 0 ? aggregationResult[0].totalWmDispatchQuantity || 0 : 0;

            totalQuantity = equipmentQuantity + totalWmDispatchQuantity;
          }
        }

        // Ensure totalQuantity is not negative
        totalQuantity = Math.max(0, totalQuantity);

        // Update the equipment record with the calculated totalQuantity
        await Equipment.findByIdAndUpdate(
          equipment._id,
          { totalQuantity: totalQuantity },
          { new: true }
        );

        updatedCount++;

        // Log progress every 100 records
        if (updatedCount % 100 === 0) {
          console.log(`Processed ${updatedCount} equipment records...`);
        }

        console.log(
          `Equipment ID: ${equipment._id}, Name: ${equipment.name}, Original Quantity: ${equipmentQuantity}, Calculated Total Quantity: ${totalQuantity}`
        );
      } catch (error) {
        console.error(`Error processing equipment ${equipment._id}:`, error.message);
        errorCount++;
      }
    }

    console.log(constantUtils.END_PROCESS);
    console.log(`Successfully updated totalQuantity for ${updatedCount} equipment records`);

    if (errorCount > 0) {
      console.log(`Encountered errors with ${errorCount} equipment records`);
    }

    // eslint-disable-next-line no-undef
    await Promise.all([
      EquipmentOrder.deleteMany({}),
      PmOrder.deleteMany({}),
      PmManageOrderEquipment.deleteMany({}),
      ShoppingCart.deleteMany({}),
      EquipmentOrderHistory.deleteMany({}),
      ReturnOrder.deleteMany({}),
      ReturnOrderHistory.deleteMany({}),
      InventoryHistory.deleteMany({ type: { $ne: 'purchase' } }),
    ]);

    return true;
  } catch (error) {
    console.error('Error in calculate-total-quantity seeder:', error);
    throw new Error(error.message);
  }
};
