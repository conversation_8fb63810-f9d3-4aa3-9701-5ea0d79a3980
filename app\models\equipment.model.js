const mongoose = require('mongoose');

const Equipment = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    equipmentNumber: {
      type: String,
    },
    serialNumber: {
      type: String,
      default: null,
    },
    productNumber: {
      type: String,
    },
    value: {
      type: Number,
    },
    weight: {
      type: Number,
    },
    equipmentType: {
      type: mongoose.Types.ObjectId,
      ref: 'equipment-type',
    },
    qrCode: [
      {
        code: { type: String },
        isActive: { type: Boolean },
        isSystemGenerated: { type: Boolean, default: true },
        createdDate: { type: Date },
        createdBy: { type: mongoose.Types.ObjectId, ref: 'user' },
      },
    ],
    equipmentImage: [
      {
        name: { type: String },
        size: { type: String },
        url: { type: String },
      },
    ],
    certificateType: [
      {
        certificateTypeId: { type: mongoose.Types.ObjectId, default: null },
        title: { type: String, default: '' },
        name: { type: String, default: '' },
        size: { type: String, default: '' },
        url: { type: String, default: '' },
        startDate: { type: Date, default: null },
        endDate: { type: Date, default: null },
      },
    ],
    warehouse: {
      type: mongoose.Types.ObjectId,
      ref: 'warehouse',
      required: true,
    },
    quantity: {
      type: Number,
    },
    equipmentLocationInWarehouse: {
      type: String,
    },
    equipmentCurrentLocation: {
      type: String,
    },
    equipmentRow: {
      type: String,
    },
    equipmentShelf: {
      type: String,
    },
    equipmentLocationFromDate: {
      type: Date,
    },
    account: {
      type: mongoose.Types.ObjectId,
      ref: 'account',
    },
    condition: {
      type: String,
      enum: ['ok', 'maintenance', 'write-off'],
      default: 'ok',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    createdBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    updatedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
    },
    deletedBy: {
      type: mongoose.Types.ObjectId,
      ref: 'user',
      default: null,
    },
    deletedAt: {
      type: Date,
      default: null,
    },
  },
  { timestamps: true }
);

module.exports = mongoose.model('equipment', Equipment);
